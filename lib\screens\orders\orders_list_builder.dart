import 'dart:math' as math;
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:phoenix/features/authentication/bloc/auth_bloc.dart';
import 'package:phoenix/features/broker_data_map/bloc/broker_data_map_bloc.dart';
import 'package:phoenix/features/common/broker_account_strategy_data.dart';
import 'package:phoenix/features/orders_state/bloc/orders_state_bloc.dart';
import 'package:phoenix/features/orders_state/model/unified_order_data.dart';
import 'package:phoenix/features/theme/bloc/theme_bloc.dart';
import 'package:phoenix/features/theme/bloc/theme_state.dart';
import 'package:phoenix/features/websocket/bloc/websocket_bloc.dart';
import 'package:phoenix/utils/app_theme.dart';
import 'package:phoenix/utils/theme_constants.dart';
import 'package:phoenix/widgets/empty_state/empty_container.dart';
import 'package:phoenix/widgets/tile/tile_generic.dart';

class OrdersListBuilder extends StatelessWidget {
  final List<dynamic> data;
  final String emptyMessage;
  final AnimationController formSheetAnimeController;
  final bool isOrderOpen;

  const OrdersListBuilder({
    super.key,
    required this.data,
    required this.emptyMessage,
    required this.formSheetAnimeController,
    this.isOrderOpen = false,
  });

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ThemeBloc, ThemeState>(
      builder: (context, themeState) {
        Future<void> pullRefresh() async {
      final authState = context.read<AuthBloc>().state; // Get current state

      if (authState is AuthAuthenticated) {
        context.read<OrdersStateBloc>().add(
              FetchOrdersState(
                clientId: authState.credentialsModel.clientId,
              ),
            );
      } else {
        // Handle unauthenticated case (e.g., show login dialog)
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text("You need to log in first")),
        );
      }
    }

    if (data.isEmpty) {
      return EmptyContainer(
          title: isOrderOpen ? "No Open Orders" : "No Orders Placed",
          message: "You can place an order from the watchlist.",
          imagePath: "images/no_orders.png");
    }

    return BlocBuilder<WebSocketBloc, WebSocketState>(
      builder: (context, state) {
        Map<int, double> stockPrices = {};

        if (state is WebSocketMultipleStockPricesUpdated) {
          stockPrices = state.stockPrices;
        }

         final brokerDataMapState = context.read<BrokerDataMapBloc>().state;
        if (brokerDataMapState is! BrokerDataMapProcessedState) {
          return Center(child: Text("Something went wrong..."));
        }

        // Group orders by bucketOrderId
        final groupedOrders = _groupOrdersByBucket(data);

        return RefreshIndicator(
          color: AppTheme.primaryColor(themeState.isDarkMode),
          backgroundColor: AppTheme.backgroundColor(themeState.isDarkMode),
          onRefresh: pullRefresh,
          child: ListView.builder(
            clipBehavior: Clip.hardEdge,
            padding: const EdgeInsets.all(6.0),
            itemCount: groupedOrders.length + 1,
            itemBuilder: (context, index) {
              if (index == groupedOrders.length) {
                // Add a SizedBox at the end
                return const SizedBox(height: 50); // Adjust height as needed
              }

              final orderGroup = groupedOrders[index];

              // If it's a single order (null bucketOrderId), display normally
              if (orderGroup.length == 1) {
                final item = orderGroup[0];
                final int zenId = item.positionCompKey.zenSecId;
                final double? priceUpdate = stockPrices[zenId];

                // Broker Account Strategy id Map
                final brokerName = brokerDataMapState
                        .brokerNameToLabelMap[item.positionCompKey.broker] ??
                    "N/A";
                final accountName = brokerDataMapState.accountIdToNameMap[
                        item.positionCompKey.accountId] ??
                    "N/A";
                final strategyName = brokerDataMapState.strategyIdToNameMap[
                        item.positionCompKey.strategyId] ??
                    "N/A";
                final brokerMetaData = BrokerAccountStrategyData(
                  brokerName: brokerName,
                  accountId: item.positionCompKey.accountId,
                  strategyId: item.positionCompKey.strategyId,
                  accountName: accountName,
                  strategyName: strategyName,
                );

                return TileGeneric(
                  data: item,
                  tileType: "zenOrder",
                  formAnimeController: formSheetAnimeController,
                  isOrderOpen: isOrderOpen,
                  prices: priceUpdate,
                  brokerAccountStrategyMapData: brokerMetaData,
                );
              } else {
                // Display as stacked cards for bucket orders
                return _buildStackedOrderCards(
                  orderGroup,
                  stockPrices,
                  brokerDataMapState,
                  themeState.isDarkMode,
                );
              }
            },
          ),
        );
      },
    );
      },
    );
  }

  /// Groups orders by bucketOrderId
  /// Returns a list of lists where each inner list contains orders with the same bucketOrderId
  /// Orders with null bucketOrderId are kept as individual groups
  List<List<UnifiedOrderData>> _groupOrdersByBucket(List<dynamic> orders) {
    final Map<int?, List<UnifiedOrderData>> bucketGroups = {};
    final List<List<UnifiedOrderData>> result = [];

    for (final order in orders) {
      final unifiedOrder = order as UnifiedOrderData;
      final bucketId = unifiedOrder.bucketOrderId;

      if (bucketId == null) {
        // Orders with null bucketOrderId are added as individual groups
        result.add([unifiedOrder]);
      } else {
        // Group orders with the same bucketOrderId
        if (!bucketGroups.containsKey(bucketId)) {
          bucketGroups[bucketId] = [];
        }
        bucketGroups[bucketId]!.add(unifiedOrder);
      }
    }

    // Add all bucket groups to result
    result.addAll(bucketGroups.values);

    return result;
  }

  /// Builds stacked order cards for bucket orders
  Widget _buildStackedOrderCards(
    List<UnifiedOrderData> orders,
    Map<int, double> stockPrices,
    dynamic brokerDataMapState,
    bool isDarkMode,
  ) {
    // Define colors for stacked cards (similar to voucher colors in reference)
    final List<Color> stackColors = [
      const Color(0xFF4CAF50), // Green
      const Color(0xFFFF9800), // Orange
      const Color(0xFFF44336), // Red
      const Color(0xFF2196F3), // Blue
      const Color(0xFF9C27B0), // Purple
    ];

    return Container(
      margin: const EdgeInsets.symmetric(vertical: 4.0),
      child: Stack(
        clipBehavior: Clip.none,
        children: [
          // Background cards to create stacking effect (show max 4 background cards)
          for (int i = math.min(orders.length - 1, 4); i > 0; i--)
            Positioned(
              top: i * 3.0,
              left: i * 2.0,
              right: -(i * 2.0),
              child: Container(
                height: 140, // Slightly taller to accommodate the stack
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(20),
                  color: stackColors[(i - 1) % stackColors.length],
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.15),
                      blurRadius: 6,
                      offset: Offset(0, i * 1.0),
                    ),
                  ],
                ),
                child: Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(20),
                    color: Colors.black.withValues(alpha: 0.1),
                  ),
                ),
              ),
            ),
          // Front card with actual content
          Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(20),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.2),
                  blurRadius: 8,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: _buildOrderTile(orders[0], stockPrices, brokerDataMapState),
          ),
          // Bucket indicator badge
          Positioned(
            top: -5,
            right: 10,
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 6),
              decoration: BoxDecoration(
                color: ThemeConstants.blue,
                borderRadius: BorderRadius.circular(15),
                border: Border.all(
                  color: isDarkMode ? Colors.grey[800]! : Colors.white,
                  width: 2,
                ),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.2),
                    blurRadius: 4,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Text(
                '${orders.length}',
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 12,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// Builds individual order tile
  Widget _buildOrderTile(
    UnifiedOrderData item,
    Map<int, double> stockPrices,
    dynamic brokerDataMapState,
  ) {
    final int zenId = item.positionCompKey.zenSecId;
    final double? priceUpdate = stockPrices[zenId];

    // Broker Account Strategy id Map
    final brokerName = brokerDataMapState
            .brokerNameToLabelMap[item.positionCompKey.broker] ??
        "N/A";
    final accountName = brokerDataMapState.accountIdToNameMap[
            item.positionCompKey.accountId] ??
        "N/A";
    final strategyName = brokerDataMapState.strategyIdToNameMap[
            item.positionCompKey.strategyId] ??
        "N/A";
    final brokerMetaData = BrokerAccountStrategyData(
      brokerName: brokerName,
      accountId: item.positionCompKey.accountId,
      strategyId: item.positionCompKey.strategyId,
      accountName: accountName,
      strategyName: strategyName,
    );

    return TileGeneric(
      data: item,
      tileType: "zenOrder",
      formAnimeController: formSheetAnimeController,
      isOrderOpen: isOrderOpen,
      prices: priceUpdate,
      brokerAccountStrategyMapData: brokerMetaData,
    );
  }
}
